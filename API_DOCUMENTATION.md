# ATMA Backend API Documentation

## Error Codes
- **400**: Bad Request - Validation error atau parameter tidak valid
- **401**: Unauthorized - Token tidak valid atau tidak ada
- **403**: Forbidden - Tidak memiliki permission untuk akses resource
- **404**: Not Found - Resource tidak ditemukan
- **409**: Conflict - Data sudah ada (misal email sudah terdaftar)
- **500**: Internal Server Error - Error pada server

---

## Auth Service API

### Public Endpoints (Tidak perlu authentication)

#### POST /auth/register
**Deskripsi**: Registrasi user baru
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
**Response**:
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>"
    },
    "token": "jwt_token_here"
  }
}
```

#### POST /auth/register/batch
**Deskripsi**: Registrasi batch user (untuk testing/seeding)
**Request Body**: Array of user objects
**Response**: Batch processing result

#### POST /auth/login
**Deskripsi**: Login user
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>"
    },
    "token": "jwt_token_here"
  }
}
```

### Protected Endpoints (Perlu authentication token)

#### GET /auth/profile
**Deskripsi**: Mendapatkan profil user yang sedang login
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "full_name": "John Doe",
      "school_origin": "SMA Negeri 1",
      "date_of_birth": "2000-01-01",
      "gender": "male",
      "token_balance": 100
    }
  }
}
```

#### PUT /auth/profile
**Deskripsi**: Update profil user
**Request Body**:
```json
{
  "full_name": "John Doe",
  "school_origin": "SMA Negeri 1",
  "date_of_birth": "2000-01-01",
  "gender": "male"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "full_name": "John Doe",
      "school_origin": "SMA Negeri 1",
      "date_of_birth": "2000-01-01",
      "gender": "male"
    }
  }
}
```

#### POST /auth/change-password
**Deskripsi**: Ganti password user
**Request Body**:
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "newpassword123"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

#### POST /auth/logout
**Deskripsi**: Logout user (invalidate token di client side)
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "message": "Logout successful"
}
```

#### GET /auth/token-balance
**Deskripsi**: Mendapatkan saldo token user
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": {
    "user_id": "uuid",
    "token_balance": 100
  }
}
```

### Internal Service Endpoints

#### POST /auth/verify-token
**Deskripsi**: Verifikasi JWT token (untuk internal service)
**Request Body**:
```json
{
  "token": "jwt_token_here"
}
```
**Response**:
```json
{
  "success": true,
  "data": {
    "valid": true,
    "user": {
      "id": "uuid",
      "email": "<EMAIL>"
    }
  }
}
```

#### PUT /auth/token-balance
**Deskripsi**: Update saldo token user (internal service only)
**Request Body**:
```json
{
  "userId": "uuid",
  "amount": 10,
  "operation": "add"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Token balance updated",
  "data": {
    "user_id": "uuid",
    "old_balance": 100,
    "new_balance": 110,
    "amount": 10,
    "operation": "add"
  }
}
```

---

## Admin Auth Endpoints

### Public Admin Endpoints

#### POST /admin/login
**Deskripsi**: Login admin
**Request Body**:
```json
{
  "username": "admin",
  "password": "admin123"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "admin": {
      "id": "uuid",
      "username": "admin",
      "email": "<EMAIL>",
      "role": "superadmin"
    },
    "token": "jwt_token_here"
  }
}
```

### Protected Admin Endpoints

#### GET /admin/profile
**Deskripsi**: Mendapatkan profil admin yang sedang login
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": {
    "admin": {
      "id": "uuid",
      "username": "admin",
      "email": "<EMAIL>",
      "full_name": "Administrator",
      "role": "superadmin"
    }
  }
}
```

#### PUT /admin/profile
**Deskripsi**: Update profil admin
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "full_name": "New Admin Name"
}
```
**Response**: Updated admin profile

#### POST /admin/change-password
**Deskripsi**: Ganti password admin
**Request Body**:
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "NewPassword123!"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

#### POST /admin/logout
**Deskripsi**: Logout admin
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "message": "Logout successful"
}
```

#### POST /admin/register (Superadmin only)
**Deskripsi**: Registrasi admin baru (hanya superadmin)
**Request Body**:
```json
{
  "username": "newadmin",
  "email": "<EMAIL>",
  "password": "NewPassword123!",
  "full_name": "New Admin",
  "role": "admin"
}
```
**Response**: New admin data with token

---

## Archive Service API

### User Endpoints (Perlu authentication)

#### GET /archive/results
**Deskripsi**: Mendapatkan hasil analisis user dengan pagination
**Query Parameters**:
- `page`: Halaman (default: 1)
- `limit`: Jumlah per halaman (default: 10, max: 100)
- `status`: Filter status (completed/processing/failed)
- `sort`: Sort by (created_at/updated_at, default: created_at)
- `order`: Sort order (asc/desc, default: desc)

**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "user_id": "uuid",
      "persona_profile": {
        "archetype": "The Innovator",
        "shortSummary": "Creative problem solver...",
        "strengths": ["Creative", "Analytical", "Leadership"],
        "weaknesses": ["Impatient", "Perfectionist"],
        "careerRecommendation": [
          {
            "careerName": "Software Engineer",
            "careerProspect": {
              "jobAvailability": "high",
              "salaryPotential": "high",
              "careerProgression": "high",
              "industryGrowth": "super high",
              "skillDevelopment": "high"
            }
          }
        ],
        "insights": ["You excel at...", "Consider developing..."],
        "workEnvironment": "Dynamic, collaborative environment",
        "roleModel": ["Elon Musk", "Steve Jobs", "Bill Gates", "Mark Zuckerberg"]
      },
      "status": "completed",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 1,
    "totalPages": 1
  }
}
```

#### GET /archive/results/:id
**Deskripsi**: Mendapatkan hasil analisis spesifik berdasarkan ID
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "user_id": "uuid",
    "persona_profile": {
      "archetype": "The Innovator",
      "shortSummary": "Creative problem solver...",
      "strengths": ["Creative", "Analytical", "Leadership"],
      "weaknesses": ["Impatient", "Perfectionist"],
      "careerRecommendation": [
        {
          "careerName": "Software Engineer",
          "careerProspect": {
            "jobAvailability": "high",
            "salaryPotential": "high",
            "careerProgression": "high",
            "industryGrowth": "super high",
            "skillDevelopment": "high"
          }
        }
      ],
      "insights": ["You excel at...", "Consider developing..."],
      "workEnvironment": "Dynamic, collaborative environment",
      "roleModel": ["Elon Musk", "Steve Jobs", "Bill Gates", "Mark Zuckerberg"]
    },
    "status": "completed",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### PUT /archive/results/:id
**Deskripsi**: Update hasil analisis (user atau internal service)
**Request Body**:
```json
{
  "persona_profile": {
    "archetype": "The Updated Innovator",
    "shortSummary": "Updated summary...",
    "strengths": ["Creative", "Analytical", "Leadership"],
    "weaknesses": ["Impatient", "Perfectionist"],
    "careerRecommendation": [
      {
        "careerName": "Software Engineer",
        "careerProspect": {
          "jobAvailability": "high",
          "salaryPotential": "high",
          "careerProgression": "high",
          "industryGrowth": "super high",
          "skillDevelopment": "high"
        }
      }
    ],
    "insights": ["Updated insights..."],
    "workEnvironment": "Updated work environment",
    "roleModel": ["Updated role models"]
  },
  "status": "completed"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Analysis result updated successfully",
  "data": {
    "id": "uuid",
    "user_id": "uuid",
    "status": "completed",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### DELETE /archive/results/:id
**Deskripsi**: Hapus hasil analisis (user only)
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "message": "Analysis result deleted successfully"
}
```

#### GET /archive/stats
**Deskripsi**: Mendapatkan statistik user
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": {
    "total_results": 5,
    "completed_results": 4,
    "processing_results": 1,
    "failed_results": 0,
    "latest_result": {
      "id": "uuid",
      "archetype": "The Innovator",
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

#### GET /archive/stats/overview
**Deskripsi**: Mendapatkan overview statistik user
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": {
    "total_assessments": 5,
    "latest_archetype": "The Innovator",
    "assessment_history": [
      {
        "date": "2024-01-01",
        "archetype": "The Innovator",
        "status": "completed"
      }
    ]
  }
}
```

### Internal Service Endpoints (Archive Service)

#### POST /archive/results
**Deskripsi**: Buat hasil analisis baru (internal service only)
**Query Parameters**:
- `batch`: true/false (default: true) - Gunakan batch processing atau tidak

**Request Body**:
```json
{
  "user_id": "uuid",
  "assessment_data": {
    "riasec": {...},
    "ocean": {...},
    "viaIs": {...}
  },
  "persona_profile": {
    "archetype": "The Innovator",
    "shortSummary": "Creative problem solver...",
    "strengths": ["Creative", "Analytical", "Leadership"],
    "weaknesses": ["Impatient", "Perfectionist"],
    "careerRecommendation": [
      {
        "careerName": "Software Engineer",
        "careerProspect": {
          "jobAvailability": "high",
          "salaryPotential": "high",
          "careerProgression": "high",
          "industryGrowth": "super high",
          "skillDevelopment": "high"
        }
      }
    ],
    "insights": ["You excel at...", "Consider developing..."],
    "workEnvironment": "Dynamic, collaborative environment",
    "roleModel": ["Elon Musk", "Steve Jobs", "Bill Gates", "Mark Zuckerberg"]
  },
  "status": "completed"
}
```
**Response**:
```json
{
  "success": true,
  "message": "Analysis result saved successfully",
  "data": {
    "id": "uuid",
    "user_id": "uuid",
    "status": "completed",
    "created_at": "2024-01-01T00:00:00Z",
    "batched": true
  }
}
```

#### POST /archive/results/batch
**Deskripsi**: Buat multiple hasil analisis sekaligus (internal service only)
**Request Body**: Array of analysis result objects
**Response**:
```json
{
  "success": true,
  "message": "Batch analysis results processed successfully",
  "data": {
    "total": 10,
    "successful": 9,
    "failed": 1,
    "results": [
      {
        "index": 0,
        "success": true,
        "id": "uuid",
        "user_id": "uuid",
        "status": "completed",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### GET /archive/stats/summary
**Deskripsi**: Mendapatkan summary statistik keseluruhan (internal service only)
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": {
    "overall": {
      "total_results": 1000,
      "total_users": 500,
      "completed_results": 950,
      "processing_results": 30,
      "failed_results": 20,
      "success_rate": 95.0
    },
    "top_archetypes": [
      {
        "archetype": "The Innovator",
        "count": 150
      }
    ],
    "recent_activity": {
      "results_last_30_days": 100,
      "active_users_last_30_days": 80
    }
  }
}
```

### Batch Processing Endpoints (Internal Service Only)

#### GET /archive/batch/stats
**Deskripsi**: Mendapatkan statistik batch processing
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "data": {
    "queue_size": 5,
    "processing": false,
    "last_batch_processed": "2024-01-01T00:00:00Z",
    "total_processed": 1000
  }
}
```

#### POST /archive/batch/process
**Deskripsi**: Force process batch queue
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "message": "Batch processing completed",
  "data": {
    "processed_items": 5,
    "successful": 4,
    "failed": 1,
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

#### POST /archive/batch/clear
**Deskripsi**: Clear batch queue (emergency operation)
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "message": "Batch queue cleared successfully",
  "data": {
    "cleared_items": 10,
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

### Demographics Endpoints (Internal Service Only)

#### GET /archive/demographics/overview
**Deskripsi**: Mendapatkan overview demografis keseluruhan
**Request Body**: Tidak ada
**Response**: Demographic overview data

#### GET /archive/demographics/archetype/:archetype
**Deskripsi**: Mendapatkan demografis untuk archetype tertentu
**Request Body**: Tidak ada
**Response**: Archetype-specific demographic data

#### GET /archive/demographics/schools
**Deskripsi**: Mendapatkan analitik berdasarkan sekolah
**Query Parameters**:
- `school`: Filter nama sekolah (optional)

**Request Body**: Tidak ada
**Response**: School-based analytics data

#### GET /archive/demographics/optimized
**Deskripsi**: Mendapatkan demografis dengan optimized query
**Query Parameters**:
- `gender`: Filter gender
- `ageMin`: Umur minimum
- `ageMax`: Umur maksimum
- `schoolOrigin`: Filter asal sekolah
- `archetype`: Filter archetype
- `limit`: Limit hasil (default: 100)

**Request Body**: Tidak ada
**Response**: Optimized demographic data

#### GET /archive/demographics/trends
**Deskripsi**: Mendapatkan trend demografis dari waktu ke waktu
**Query Parameters**:
- `period`: Periode waktu (day/week/month/year)
- `limit`: Jumlah periode yang diambil

**Request Body**: Tidak ada
**Response**: Demographic trends data

### Admin Endpoints (Archive Service)

#### GET /admin/users
**Deskripsi**: Mendapatkan semua user dengan pagination dan filtering
**Query Parameters**:
- `page`: Halaman (default: 1)
- `limit`: Jumlah per halaman (default: 10, max: 100)
- `search`: Search term untuk email
- `sortBy`: Sort by field (email/token_balance/created_at/updated_at, default: created_at)
- `sortOrder`: Sort order (ASC/DESC, default: DESC)

**Request Body**: Tidak ada
**Response**: Paginated user list with details

#### GET /admin/users/:userId
**Deskripsi**: Mendapatkan detail user berdasarkan ID
**Request Body**: Tidak ada
**Response**: Detailed user information

#### PUT /admin/users/:userId/token-balance
**Deskripsi**: Update saldo token user
**Request Body**:
```json
{
  "token_balance": 100,
  "action": "set"
}
```
**Response**: Updated token balance information

#### DELETE /admin/users/:userId
**Deskripsi**: Hapus user (soft delete)
**Request Body**: Tidak ada
**Response**:
```json
{
  "success": true,
  "message": "User deleted successfully"
}
```

---

## Authentication Headers

### User Authentication
```
Authorization: Bearer <jwt_token>
```

### Admin Authentication
```
Authorization: Bearer <admin_jwt_token>
```

### Internal Service Authentication
```
X-Service-Key: <internal_service_key>
X-Internal-Service: true
```

---

## Notes

1. **Validation**: Semua endpoint memiliki validasi input menggunakan Joi schema
2. **Pagination**: Endpoint yang mengembalikan list menggunakan pagination dengan format standar
3. **Error Handling**: Semua error mengikuti format response yang konsisten
4. **Rate Limiting**: Auth endpoints memiliki rate limiting untuk keamanan
5. **Batch Processing**: Archive service mendukung batch processing untuk performa yang lebih baik
6. **Internal Services**: Beberapa endpoint hanya bisa diakses oleh internal service dengan authentication khusus
```
